package org.example

import eu.mihosoft.jcsg.Cube
import eu.mihosoft.jcsg.FileUtil
import javafx.application.Application
import javafx.scene.PerspectiveCamera
import javafx.scene.Scene
import javafx.scene.SceneAntialiasing
import javafx.scene.input.KeyCode
import javafx.scene.layout.StackPane
import javafx.scene.paint.Color
import javafx.scene.paint.PhongMaterial
import javafx.scene.shape.MeshView
import javafx.scene.transform.Rotate
import javafx.scene.transform.Scale
import javafx.scene.transform.Translate
import javafx.stage.Stage
import kotlinx.coroutines.*
import kotlin.io.path.Path
import kotlin.time.Clock
import kotlin.time.ExperimentalTime

class CadViewer: Application() {
    val mouseSensitivity = 0.5
    val zoomSensibility = 0.005
    val movementSensibility = 5;
    var zoom = 1.0
    var xOffset: Double = 0.0;
    var yOffset: Double = 0.0;
    var zOffset: Double = 0.0;
    var rotateX: Double = 0.0;
    var rotateY: Double = 0.0;
    private val scope = CoroutineScope(Dispatchers.Default)
    lateinit var meshView: MeshView;

    @OptIn(DelicateCoroutinesApi::class)

    override fun start(primaryStage: Stage?) {
        val cube = createObject();
        val meshContainer = cube.toJavaFXMesh()
        meshView = meshContainer.getAsMeshViews()[0];
        meshView.material = PhongMaterial(Color.ALICEBLUE)
        val root = StackPane()
        root.children.add(meshView)
        primaryStage!!.title = "Visualizador CAD"
        primaryStage.scene = Scene(root, 800.0, 600.0, true, SceneAntialiasing.BALANCED)
        primaryStage.scene.camera = PerspectiveCamera(false)
        primaryStage.scene.fill = Color.GRAY
        primaryStage.show()
        setupKeyboardControls(primaryStage)
        setupMouseControls(primaryStage)
        val outputPath = Path("output.stl")
        println("Output path: ${outputPath.toAbsolutePath()}")
        FileUtil.write(outputPath, cube.toStlString())
    }

    @OptIn(DelicateCoroutinesApi::class, ExperimentalTime::class)
    private fun setupKeyboardControls(primaryStage: Stage) {
        val keysPressed = object {
            var w = false;
            var s = false;
            var a = false;
            var d = false;
            var space = false;
            var shift = false;
            var r = false;
        }
        fun setValue(code: KeyCode?, value: Boolean) {
            when (code) {
                KeyCode.W -> keysPressed.w = value;
                KeyCode.S -> keysPressed.s = value;
                KeyCode.A -> keysPressed.a = value;
                KeyCode.D -> keysPressed.d = value;
                KeyCode.SHIFT -> keysPressed.shift = value;
                KeyCode.SPACE -> keysPressed.space = value;
                KeyCode.R -> keysPressed.r = value;
                else -> {}
            }
        }
        primaryStage.scene.setOnKeyPressed { setValue(it.code, true) }
        primaryStage.scene.setOnKeyReleased { setValue(it.code, false) }
        GlobalScope.launch(Dispatchers.Main) {
            while (true) {
                val lastExec = Clock.System.now().epochSeconds
                if (keysPressed.w) {
                    forward()
                } else if (keysPressed.s) {
                    backward()
                }
                if (keysPressed.a) {
                    left()
                } else if (keysPressed.d) {
                    right()
                }
                if (keysPressed.space) {
                    up()
                } else if (keysPressed.shift) {
                    down()
                }
                if (keysPressed.r) {
                    reset()
                }
                delay(100 / 6 - (Clock.System.now().epochSeconds - lastExec))
            }
        }
    }

    private fun setupMouseControls(primaryStage: Stage) {
        var lastMouseX: Double = 0.0
        var lastMouseY: Double = 0.0
        var isDragging: Boolean = false
        primaryStage.scene.setOnMousePressed { event ->
            lastMouseX = event.sceneX
            lastMouseY = event.sceneY
            isDragging = true
        }

        primaryStage.scene.setOnMouseDragged { event ->
            if (isDragging) {
                updateTransforms {
                    rotateY -= (event.sceneX - lastMouseX) * mouseSensitivity
                    rotateX += (event.sceneY - lastMouseY) * mouseSensitivity
                    rotateX = rotateX.coerceIn(-90.0, 90.0)
                    lastMouseX = event.sceneX
                    lastMouseY = event.sceneY
                }
            }
        }

        primaryStage.scene.setOnMouseReleased {
            isDragging = false
        }

        primaryStage.scene.setOnScroll {
            println(it);
            updateTransforms {
                zoom *= 1 + zoomSensibility * it.deltaY
            }
        }
    }

    fun updateTransforms(r: Runnable) {
        r.run()
        meshView.transforms.clear()
        meshView.transforms.addAll(
            Rotate(rotateX, Rotate.X_AXIS),
            Rotate(rotateY, Rotate.Y_AXIS),
            Scale(zoom, zoom, zoom),
            Translate(xOffset, yOffset, zOffset),
        )
    }

    fun left() = updateTransforms {
        val radY = Math.toRadians(rotateY)
        xOffset += movementSensibility * Math.cos(radY)
        zOffset += movementSensibility * Math.sin(radY)
    }

    fun right() = updateTransforms {
        val radY = Math.toRadians(rotateY)
        xOffset -= movementSensibility * Math.cos(radY)
        zOffset -= movementSensibility * Math.sin(radY)
    }

    fun forward() = updateTransforms {
        val radY = Math.toRadians(rotateY)
        val radX = Math.toRadians(rotateX)
        xOffset += movementSensibility * Math.sin(radY) * Math.cos(radX)
        yOffset += movementSensibility * Math.sin(radX)
        zOffset -= movementSensibility * Math.cos(radY) * Math.cos(radX)
    }

    fun backward() = updateTransforms {
        val radY = Math.toRadians(rotateY)
        val radX = Math.toRadians(rotateX)
        xOffset -= movementSensibility * Math.sin(radY) * Math.cos(radX)
        yOffset -= movementSensibility * Math.sin(radX)
        zOffset += movementSensibility * Math.cos(radY) * Math.cos(radX)
    }

    fun up() = updateTransforms {
        val radY = Math.toRadians(rotateY)
        val radX = Math.toRadians(rotateX)
        xOffset += movementSensibility * Math.sin(radY) * Math.sin(radX)
        yOffset -= movementSensibility * Math.cos(radX)
        zOffset -= movementSensibility * Math.cos(radY) * Math.sin(radX)
    }

    fun down() = updateTransforms {
        val radY = Math.toRadians(rotateY)
        val radX = Math.toRadians(rotateX)
        xOffset -= movementSensibility * Math.sin(radY) * Math.sin(radX)
        yOffset += movementSensibility * Math.cos(radX)
        zOffset += movementSensibility * Math.cos(radY) * Math.sin(radX)
    }
    fun reset() = updateTransforms {
        zoom = 1.0
        xOffset = 0.0
        yOffset = 0.0
        zOffset = 0.0
        rotateX = 0.0
        rotateY = 0.0
    }
}
fun main() {
    Application.launch(CadViewer::class.java);
}