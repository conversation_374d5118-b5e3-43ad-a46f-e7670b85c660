package org.example

import eu.mihosoft.jcsg.*
import eu.mihosoft.vvecmath.Transform
import eu.mihosoft.vvecmath.Vector3d


class Primitives {
    fun cube(center: Vector3d, dimensions: Vector3d) = Cube()
    fun cube(w: Double, h: Double, d: Double) = Cube(w, h, d)
    fun cube(size: Double) = Cube(size)

    Cylinder(Vector3d start, Vector3d end, double radius, int numSlices)
    Cylinder(Vector3d start, Vector3d end, double startRadius, double endRadius, int numSlices)
    public

    ylinder(double radius, double height, int numSlices
    Cylinder(double startRadius, double endRadius, double height, int numSlices)


}


@DslMarker
annotation class BandejaDsl

@BandejaDsl
class BandejasProperties {
    @BandejaDsl
    class BandejaProperties {
        var height = 1.0
    }
    val bandejaList = mutableListOf<BandejaProperties>()
    var width = 10.0
    var depth = 10.0
    var pitch = 1.0
    fun bandeja(init: BandejaProperties.() -> Unit) {
        val bandejaProperties = BandejaProperties()
        bandejaProperties.init()
        bandejaList.add(bandejaProperties)
    }
}


fun bandejas(init: BandejasProperties.() -> Unit): CSG {
    val p = BandejasProperties()
    p.init();
    fun walls(h: Double): CSG {
        return Cube(p.width, h, p.depth).toCSG().
    }
    var h = 0.0;
    return Cube(p.width, p.pitch, p.depth).toCSG()
//    return props.bandejaList.map { bandeja ->
//        val lastH = h
//        h += bandeja.height
//        Cube(props.width, props.pitch, props.depth).toCSG()
//            .transformed(Transform.unity().translate(0.0, lastH,0.0))
//    }.reduce { acc, c -> acc.union(c) }
}



fun createObject(): CSG {
    return bandejas {
        width = 100.0
        depth = 100.0
        pitch = 1.0
        bandeja {
            height = 10.0
        }
        bandeja {
            height = 2.0
        }
    }
}